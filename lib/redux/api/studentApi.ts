import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';

// Student data interfaces
export interface Address {
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
}

export interface MaritalStatus {
  status: string;
  spouseName: string;
  spousePhone: string;
  spousePassport: string;
}

export interface Sponsor {
  name: string;
  relation: string;
  phone: string;
  email: string;
}

export interface EmergencyContact {
  lastName: string;
  middleName: string;
  firstName: string;
  phoneHome: string;
  phoneMobile: string;
  relation: string;
}

export interface SocialLink {
  platform: string;
  url: string;
}

export interface StudentData {
  studentId?: string | number; // Optional for create, required for update
  firstName: string;
  lastName: string;
  nameInNative: string;
  email: string;
  phone: string;
  guardianPhone: string;
  dateOfBirth: string;
  gender: string;
  fatherName: string;
  motherName: string;
  nid: string;
  passport: string;
  presentAddress: Address;
  permanentAddress: Address;
  maritalStatus: MaritalStatus;
  sponsor: Sponsor;
  emergencyContact: EmergencyContact;
  preferredSubject: string[];
  preferredCountry: string[];
  socialLinks: SocialLink[];
  reference: string;
  note: string;
}

export interface StudentResponse {
  success: boolean;
  message: string;
  data?: StudentData;
}

// Education data interfaces
export interface AcademicRecord {
  foreignDegree: boolean;
  nameOfExam: string;
  institute: string;
  subject: string;
  board: string;
  grade: string;
  passingYear: string;
}

export interface ProficiencyScore {
  overall: number;
  R: number;
  L: number;
  W: number;
  S: number;
}

export interface ProficiencyRecord {
  nameOfExam: string;
  score: ProficiencyScore;
  examDate: string;
  expiryDate: string;
  note: string;
}

export interface Publication {
  subject: string;
  journal: string;
  publicationDate: string;
  link: string;
}

export interface OtherActivity {
  subject: string;
  certificationLink: string;
  startDate: string;
  endDate: string;
}

export interface EducationData {
  academic: AcademicRecord[];
  proficiency: ProficiencyRecord[];
  publications: Publication[];
  otherActivities: OtherActivity[];
}

export interface EducationResponse {
  success: boolean;
  message: string;
  data?: EducationData;
}

export interface DocumentsData {
  academicSections: string[];
  proficiencySections: string[];
  sponsorName: string;
  takeDependents: boolean;
  dependents: Array<{
    name: string;
    passport: string;
  }>;
  children: Array<{
    name: string;
    passport: string;
  }>;
  files: {
    [key: string]: File;
  };
}

export interface DocumentsResponse {
  success: boolean;
  message: string;
  data?: any;
  documents?: any;
}

export interface ApplicationData {
  universityId: number;
  universityCountryId: number;
  universityCountryCampus: number;
  programId: number;
  intakeId: number;
  courseId: number;
  note: string;
  status: string;
  paymentStatus: string;
  applicationType: string;
  applicationId: string;
  currentStage: string;
  overallProgress: number;
  totalAmount: number;
  deliveryMethod: string;
  deadlineDate: string;
}

export interface ApplicationsPayload {
  studentId: string;
  applications: ApplicationData[];
}

export interface ApplicationResponse {
  success: boolean;
  message: string;
  status?: string;
  data?: any;
}

export interface UniversitiesResponse {
  success: boolean;
  status?: string;
  message: string;
  data: any;
}

const STUDENT_BASE_URL = process.env.NEXT_PUBLIC_STUDENT_BASE_URL;

export const studentApi = createApi({
  reducerPath: 'studentApi',
  baseQuery: fetchBaseQuery({
    baseUrl: STUDENT_BASE_URL,
    prepareHeaders: (headers, { endpoint }) => {
      const token = Cookies.get('accessToken');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      // Don't set Content-Type for FormData requests
      if (endpoint !== 'saveDocuments') {
        headers.set('Content-Type', 'application/json');
      }
      return headers;
    },
    responseHandler: async (response) => {
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          url: response.url,
          error: errorData
        });
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    },
  }),
  tagTypes: ['Student'],
  endpoints: (builder) => ({
    saveStudent: builder.mutation<StudentResponse, StudentData>({
      query: (studentData) => ({
        url: '/student',
        method: 'POST',
        body: studentData,
      }),
      invalidatesTags: ['Student'],
    }),
    getStudent: builder.query<StudentResponse, string | number>({
      query: (id) => `/student/${id}`,
      providesTags: ['Student'],
    }),
    deleteStudent: builder.mutation<StudentResponse, string | number>({
      query: (id) => ({
        url: `/student/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Student'],
    }),
    saveEducation: builder.mutation<EducationResponse, { studentId: string | number; educationData: EducationData }>({
      query: ({ studentId, educationData }) => ({
        url: `/student/${studentId}/academic`,
        method: 'POST',
        body: educationData,
      }),
      invalidatesTags: ['Student'],
    }),
    getEducation: builder.query<EducationResponse, string | number>({
      query: (studentId) => `/student/${studentId}/academic`,
      providesTags: ['Student'],
    }),
    saveDocuments: builder.mutation<DocumentsResponse, { studentId: string | number; documentsData: FormData }>({
      query: ({ studentId, documentsData }) => ({
        url: `/students/${studentId}/documents`,
        method: 'POST',
        body: documentsData,
      }),
      invalidatesTags: ['Student'],
    }),
    getDocuments: builder.query<DocumentsResponse, string | number>({
      query: (studentId) => `/students/${studentId}/documents`,
      providesTags: ['Student'],
    }),
    saveApplications: builder.mutation<ApplicationResponse, ApplicationsPayload>({
      query: (applicationsPayload) => ({
        url: 'http://localhost:4007/api/student/applications',
        method: 'POST',
        body: applicationsPayload,
      }),
      invalidatesTags: ['Student'],
    }),
    getApplications: builder.query<ApplicationResponse, string | number>({
      query: (studentId) => `http://localhost:4007/api/student/applications?studentId=${studentId}`,
      providesTags: ['Student'],
    }),
    getUniversities: builder.query<UniversitiesResponse, void>({
      query: () => '/student/universities/application',
      providesTags: ['Student'],
    }),
  }),
});

export const {
  useSaveStudentMutation,
  useGetStudentQuery,
  useDeleteStudentMutation,
  useSaveEducationMutation,
  useGetEducationQuery,
  useSaveDocumentsMutation,
  useGetDocumentsQuery,
  useSaveApplicationsMutation,
  useGetApplicationsQuery,
  useGetUniversitiesQuery,
} = studentApi;
