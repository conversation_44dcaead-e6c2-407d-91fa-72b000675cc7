import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { StudentData, EducationData, ApplicationData, DocumentsData } from '../api/studentApi';

interface StudentState {
  personalData: StudentData | null;
  studentId: string | number | null;
  isLoading: boolean;
  error: string | null;
  formData: Partial<StudentData>;
  educationData: EducationData | null;
  applicationData: ApplicationData[] | null;
  documentsData: DocumentsData | null;
}

const initialState: StudentState = {
  personalData: null,
  studentId: null,
  isLoading: false,
  error: null,
  formData: {},
  educationData: null,
  applicationData: null,
  documentsData: null,
};

const studentSlice = createSlice({
  name: 'student',
  initialState,
  reducers: {
    setCurrentStudent: (state, action: PayloadAction<StudentData>) => {
      state.personalData = action.payload;
      state.error = null;
    },
    setStudentId: (state, action: PayloadAction<string | number>) => {
      state.studentId = action.payload;
      state.error = null;
    },
    clearCurrentStudent: (state) => {
      state.personalData = null;
      state.educationData = null;
      state.applicationData = null;
      state.documentsData = null;
      state.studentId = null;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateFormData: (state, action: PayloadAction<Partial<StudentData>>) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    clearFormData: (state) => {
      state.formData = {};
    },
    setFormData: (state, action: PayloadAction<Partial<StudentData>>) => {
      state.formData = action.payload;
    },
    setEducationData: (state, action: PayloadAction<EducationData>) => {
      state.educationData = action.payload;
      state.error = null;
    },
    clearEducationData: (state) => {
      state.educationData = null;
    },
    setApplicationData: (state, action: PayloadAction<ApplicationData[]>) => {
      state.applicationData = action.payload;
      state.error = null;
    },
    clearApplicationData: (state) => {
      state.applicationData = null;
    },
    setDocumentsData: (state, action: PayloadAction<DocumentsData>) => {
      state.documentsData = action.payload;
      state.error = null;
    },
    clearDocumentsData: (state) => {
      state.documentsData = null;
    },
  },
});

export const {
  setCurrentStudent,
  setStudentId,
  clearCurrentStudent,
  setLoading,
  setError,
  clearError,
  updateFormData,
  clearFormData,
  setFormData,
  setEducationData,
  clearEducationData,
  setApplicationData,
  clearApplicationData,
  setDocumentsData,
  clearDocumentsData,
} = studentSlice.actions;

export default studentSlice.reducer;
