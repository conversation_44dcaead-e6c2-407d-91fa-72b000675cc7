import { useState, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useSaveDocumentsMutation, useGetDocumentsQuery } from '@/lib/redux/api/studentApi';
import { setDocumentsData } from '@/lib/redux/slices/studentSlice';

export const useDocumentsData = (studentId?: string | number) => {
  const dispatch = useDispatch();
  const [saveDocuments] = useSaveDocumentsMutation();
  
  // Fetch documents data
  const { 
    data: documentsResponse, 
    isLoading: isDocumentsLoading, 
    error: documentsError,
    refetch: refetchDocuments 
  } = useGetDocumentsQuery(studentId!, {
    skip: !studentId,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Save documents data
  const saveDocumentsData = useCallback(async (documentsData: FormData) => {
    if (!studentId) {
      setError('Student ID is required');
      return { success: false, error: 'Student ID is required' };
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await saveDocuments({
        studentId,
        documentsData
      }).unwrap();

      if (result.success && result.data) {
        // Store documents data in Redux
        dispatch(setDocumentsData(result.data));
        return { success: true, data: result.data };
      }

      return { success: false, error: result.message || 'Failed to save documents' };
    } catch (error: any) {
      const errorMessage = error?.data?.message || 'Failed to save documents';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [studentId, saveDocuments, dispatch]);

  // Fetch documents data
  const fetchDocumentsData = useCallback(async () => {
    if (!studentId) {
      setError('Student ID is required');
      return { success: false, error: 'Student ID is required' };
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await refetchDocuments().unwrap();

      if (result.success && result.data) {
        // Store documents data in Redux
        dispatch(setDocumentsData(result.data));
        return { success: true, data: result.data };
      }

      return { success: false, error: result.message || 'Failed to fetch documents' };
    } catch (error: any) {
      const errorMessage = error?.data?.message || 'Failed to fetch documents';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [studentId, refetchDocuments, dispatch]);

  return {
    saveDocumentsData,
    fetchDocumentsData,
    documentsData: documentsResponse?.data,
    isLoading: isLoading || isDocumentsLoading,
    error: error || documentsError,
    refetchDocuments,
  };
};
